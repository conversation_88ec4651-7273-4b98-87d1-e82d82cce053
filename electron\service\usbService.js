"use strict";

const { logger } = require("ee-core/log");
const { SerialPort } = require("serialport");
const { ReadlineParser } = require("@serialport/parser-readline");
const EventEmitter = require("events");
const MessageParser = require("../utils/messageParser");
const MessageEncoder = require("../utils/messageEncoder");
const globalStateManager = require("./globalStateManager");

/**
 * USB通信服务
 * 实现与安卓3588设备的USB串口通信
 */
class UsbService extends EventEmitter {
  static instance = null;

  static getInstance() {
    if (!UsbService.instance) {
      UsbService.instance = new UsbService();
    }
    return UsbService.instance;
  }

  constructor() {
    super();
    if (UsbService.instance) {
      return UsbService.instance;
    }

    this.port = null;
    this.parser = null;
    this.isConnected = false;
    this.isConnecting = false;
    
    // USB连接配置
    this.config = {
      baudRate: 115200,
      dataBits: 8,
      stopBits: 1,
      parity: 'none',
      autoOpen: false,
      highWaterMark: 64 * 1024 // 64KB buffer
    };
    
    // 设备检测配置
    this.deviceFilters = [
      { vendorId: '2207', productId: '0011' }, // RK3588 USB设备
      { vendorId: '2207' }, // Rockchip设备
      { manufacturer: /rockchip/i },
      { manufacturer: /android/i }
    ];
    
    // 重连配置
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.reconnectTimer = null;
    
    // 心跳配置
    this.heartbeatInterval = 5000;
    this.heartbeatTimer = null;
    this.lastHeartbeatResponse = Date.now();
    this.heartbeatTimeout = 10000;
    
    // 消息发送配置
    this.messageTimer1100 = null;
    this.messageInterval1100 = 20000;
    
    UsbService.instance = this;
  }

  /**
   * 连接USB设备
   */
  async connect() {
    if (this.isConnected || this.isConnecting) {
      logger.warn("[UsbService] Already connected or connecting");
      return;
    }

    this.isConnecting = true;
    this.emit('connecting');

    try {
      // 查找可用的USB设备
      const devicePath = await this._findUsbDevice();
      if (!devicePath) {
        throw new Error("No compatible USB device found");
      }

      // 创建串口连接
      await this._createSerialConnection(devicePath);
      
      logger.info(`[UsbService] Connected to USB device: ${devicePath}`);
      this.isConnected = true;
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      
      // 清除重连定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      
      // 启动心跳和消息发送
      this._startHeartbeat();
      this._startSendMessage();
      
      this.emit('connected');
      
    } catch (error) {
      this.isConnecting = false;
      logger.error("[UsbService] Failed to connect:", error);
      this.emit('error', error);
      this._scheduleReconnect();
      throw error;
    }
  }

  /**
   * 断开USB连接
   */
  async disconnect() {
    logger.info("[UsbService] Disconnecting USB service");
    
    this._stopHeartbeat();
    this._stopSendMessage();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.port && this.port.isOpen) {
      try {
        await new Promise((resolve, reject) => {
          this.port.close((error) => {
            if (error) {
              reject(error);
            } else {
              resolve();
            }
          });
        });
      } catch (error) {
        logger.error("[UsbService] Error closing port:", error);
      }
    }
    
    this.port = null;
    this.parser = null;
    this.isConnected = false;
    this.isConnecting = false;
    
    this.emit('disconnected');
    logger.info("[UsbService] USB service disconnected");
  }

  /**
   * 发送消息
   * @param {Buffer} message - 要发送的消息
   */
  async sendMessage(message) {
    if (!this.isConnected || !this.port || !this.port.isOpen) {
      throw new Error("USB connection not available");
    }

    return new Promise((resolve, reject) => {
      this.port.write(message, (error) => {
        if (error) {
          logger.error("[UsbService] Failed to send message:", error);
          reject(error);
        } else {
          logger.debug(`[UsbService] Message sent: ${message.length} bytes`);
          resolve();
        }
      });
    });
  }

  /**
   * 检查连接健康状态
   */
  async isHealthy() {
    if (!this.isConnected) {
      return false;
    }
    
    // 检查心跳响应时间
    const timeSinceLastHeartbeat = Date.now() - this.lastHeartbeatResponse;
    if (timeSinceLastHeartbeat > this.heartbeatTimeout) {
      logger.warn(`[UsbService] Heartbeat timeout: ${timeSinceLastHeartbeat}ms`);
      return false;
    }
    
    // 检查串口状态
    if (!this.port || !this.port.isOpen) {
      return false;
    }
    
    return true;
  }

  /**
   * 获取连接信息
   */
  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      portPath: this.port ? this.port.path : null,
      reconnectAttempts: this.reconnectAttempts,
      lastHeartbeat: this.lastHeartbeatResponse
    };
  }

  /**
   * 查找USB设备
   */
  async _findUsbDevice() {
    try {
      const ports = await SerialPort.list();
      logger.debug(`[UsbService] Found ${ports.length} serial ports`);
      
      // 首先尝试通过设备过滤器匹配
      for (const filter of this.deviceFilters) {
        const matchedPort = ports.find(port => {
          if (filter.vendorId && port.vendorId !== filter.vendorId) {
            return false;
          }
          if (filter.productId && port.productId !== filter.productId) {
            return false;
          }
          if (filter.manufacturer && !filter.manufacturer.test(port.manufacturer || '')) {
            return false;
          }
          return true;
        });
        
        if (matchedPort) {
          logger.info(`[UsbService] Found matching device: ${matchedPort.path} (${matchedPort.manufacturer})`);
          return matchedPort.path;
        }
      }
      
      // 如果没有找到匹配的设备，尝试使用配置中的路径
      const configPath = await globalStateManager.get("usb.devicePath");
      if (configPath) {
        const configPort = ports.find(port => port.path === configPath);
        if (configPort) {
          logger.info(`[UsbService] Using configured device path: ${configPath}`);
          return configPath;
        }
      }
      
      // 最后尝试使用第一个可用的串口（仅在开发模式下）
      if (process.env.NODE_ENV === 'development' && ports.length > 0) {
        logger.warn(`[UsbService] Using first available port for development: ${ports[0].path}`);
        return ports[0].path;
      }
      
      return null;
    } catch (error) {
      logger.error("[UsbService] Error listing serial ports:", error);
      throw error;
    }
  }

  /**
   * 创建串口连接
   * @param {string} devicePath - 设备路径
   */
  async _createSerialConnection(devicePath) {
    return new Promise((resolve, reject) => {
      this.port = new SerialPort({
        path: devicePath,
        ...this.config
      });

      // 创建数据解析器
      this.parser = this.port.pipe(new ReadlineParser({ delimiter: '\r\n' }));

      // 绑定事件处理器
      this.port.on('open', () => {
        logger.info(`[UsbService] Serial port opened: ${devicePath}`);
        resolve();
      });

      this.port.on('error', (error) => {
        logger.error("[UsbService] Serial port error:", error);
        this.emit('error', error);
        if (!this.isConnected) {
          reject(error);
        }
      });

      this.port.on('close', () => {
        logger.info("[UsbService] Serial port closed");
        this.isConnected = false;
        this.emit('disconnected');
        this._scheduleReconnect();
      });

      // 处理接收到的数据
      this.port.on('data', (data) => {
        this._handleRawData(data);
      });

      // 打开串口
      this.port.open();
    });
  }

  /**
   * 处理原始数据
   * @param {Buffer} data - 接收到的原始数据
   */
  _handleRawData(data) {
    try {
      logger.debug(`[UsbService] Received ${data.length} bytes`);

      // 解析消息
      const message = MessageParser.parse(data, "usb");
      this._handleMessage(message);

    } catch (error) {
      logger.error("[UsbService] Failed to parse received data:", error);
    }
  }

  /**
   * 处理解析后的消息
   * @param {object} message - 解析后的消息
   */
  async _handleMessage(message) {
    try {
      // 更新心跳响应时间
      this.lastHeartbeatResponse = Date.now();

      // 发送消息事件
      this.emit('message', message);

      // 处理特定类型的消息
      switch (message.id) {
        case 2048:
          // 心跳响应
          logger.debug("[UsbService] Heartbeat response received");
          break;
        case 20:
          await this._handleMessageID20(message);
          break;
        case 11:
          await this._handleMessageID11(message);
          break;
        default:
          logger.debug(`[UsbService] Received message ID: ${message.id}`);
          break;
      }
    } catch (error) {
      logger.error("[UsbService] Error handling message:", error);
    }
  }

  /**
   * 处理ID为20的消息
   * @param {object} message - 消息对象
   */
  async _handleMessageID20(message) {
    logger.debug("[UsbService] Handling message ID 20");

    // 如果是页面300的消息，更新全局状态
    if (message.jsonData && message.jsonData.page === 300 && message.jsonData.payload) {
      try {
        await globalStateManager.setAll(message.jsonData.payload);
        logger.info("[UsbService] Global state updated from USB message");
      } catch (error) {
        logger.error("[UsbService] Error updating global state:", error);
      }
    }
  }

  /**
   * 处理ID为11的消息
   * @param {object} message - 消息对象
   */
  async _handleMessageID11(message) {
    logger.debug("[UsbService] Handling message ID 11");

    // 发送心跳响应
    await this._sendHeartbeatResponse(message);
  }

  /**
   * 发送心跳响应
   * @param {object} message - 接收到的消息
   */
  async _sendHeartbeatResponse(message) {
    try {
      const padStatus = await globalStateManager.get("pad.status");

      const body = {
        senderStatus: padStatus,
        confirmedTransmitCount: message.transmitCount,
      };

      const msgBody = await MessageEncoder.buildBody(12, body);
      const head = {
        vehicleType: 0,
        dataUnitLength: msgBody.length,
        transmitCount: MessageEncoder.getTransmitCount(11),
      };

      const msgHead = MessageEncoder.buildHead(head);
      const mergedBuffer = Buffer.concat([msgHead, msgBody]);

      await this.sendMessage(mergedBuffer);
      logger.debug("[UsbService] Heartbeat response sent");

    } catch (error) {
      logger.error("[UsbService] Failed to send heartbeat response:", error);
    }
  }

  /**
   * 启动心跳
   */
  _startHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = setInterval(async () => {
      try {
        await this._sendHeartbeat();
      } catch (error) {
        logger.error("[UsbService] Heartbeat failed:", error);
      }
    }, this.heartbeatInterval);
  }

  /**
   * 停止心跳
   */
  _stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 发送心跳
   */
  async _sendHeartbeat() {
    const body = {
      vehicleSubType: 0,
      tabletStatus: 64, // 64远控
      failureLevel: 255, // 255没有失效
    };

    const msgBody = await MessageEncoder.buildBody(1100, body);
    const head = {
      vehicleType: 0,
      dataUnitLength: msgBody.length,
      transmitCount: MessageEncoder.getTransmitCount(1100),
    };

    const msgHead = MessageEncoder.buildHead(head);
    const mergedBuffer = Buffer.concat([msgHead, msgBody]);

    await this.sendMessage(mergedBuffer);
    logger.debug("[UsbService] Heartbeat sent");
  }

  /**
   * 启动定时消息发送
   */
  _startSendMessage() {
    if (this.messageTimer1100) {
      clearInterval(this.messageTimer1100);
    }

    this.messageTimer1100 = setInterval(async () => {
      try {
        await this._sendHeartbeat();
      } catch (error) {
        logger.error("[UsbService] Failed to send periodic message:", error);
      }
    }, this.messageInterval1100);

    // 延迟获取所有数据
    setTimeout(() => {
      this._getAllDataFromAndroid();
    }, 5000);
  }

  /**
   * 停止定时消息发送
   */
  _stopSendMessage() {
    if (this.messageTimer1100) {
      clearInterval(this.messageTimer1100);
      this.messageTimer1100 = null;
    }
  }

  /**
   * 获取所有Android数据
   */
  async _getAllDataFromAndroid() {
    try {
      const body = {
        communicationType: 2,
        jsonData: {
          page: 300,
          type: "params",
          payload: {
            route: "all",
          },
        },
      };

      await this._sendMessageID20(body);
    } catch (error) {
      logger.error("[UsbService] Failed to get all data from Android:", error);
    }
  }

  /**
   * 发送ID为20的消息
   * @param {object} body - 消息体
   */
  async _sendMessageID20(body) {
    const msgBody = await MessageEncoder.buildBody(20, body);
    const head = {
      vehicleType: 0,
      dataUnitLength: msgBody.length,
      transmitCount: MessageEncoder.getTransmitCount(20),
    };

    const msgHead = MessageEncoder.buildHead(head);
    const mergedBuffer = Buffer.concat([msgHead, msgBody]);

    await this.sendMessage(mergedBuffer);
    logger.debug("[UsbService] Message ID 20 sent");
  }

  /**
   * 安排重连
   */
  _scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.warn("[UsbService] Max reconnect attempts reached");
      return;
    }

    if (this.reconnectTimer) {
      return; // 已经在重连中
    }

    this.reconnectAttempts++;

    this.reconnectTimer = setTimeout(async () => {
      this.reconnectTimer = null;
      logger.info(`[UsbService] Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      try {
        await this.connect();
      } catch (error) {
        logger.error("[UsbService] Reconnect failed:", error);
        this._scheduleReconnect();
      }
    }, this.reconnectInterval);
  }
}

module.exports = UsbService.getInstance();
